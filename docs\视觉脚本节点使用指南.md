# 视觉脚本节点使用指南

## 概述

本指南详细介绍了DL引擎视觉脚本系统中413个节点的使用方法、最佳实践和常见应用场景。

## 快速入门

### 基础概念

1. **节点 (Node)**: 执行特定功能的基本单元
2. **插槽 (Socket)**: 节点的输入输出接口
3. **连接 (Connection)**: 节点间的数据流连接
4. **图形 (Graph)**: 由节点和连接组成的完整脚本

### 节点类型

- **事件节点**: 脚本执行的触发点
- **流程节点**: 控制执行流程
- **数据节点**: 处理和转换数据
- **功能节点**: 执行具体功能

## 核心节点使用详解

### 1. 事件节点

#### OnStartNode (开始事件节点)
```typescript
// 使用场景：脚本初始化
const startNode = new OnStartNode({
  id: 'start_1',
  type: 'core/events/onStart'
});

// 连接到初始化逻辑
startNode.connectTo(initializeNode, 'flow');
```

**最佳实践**:
- 用于设置初始变量
- 加载必要资源
- 建立网络连接

#### OnUpdateNode (更新事件节点)
```typescript
// 使用场景：持续监控和更新
const updateNode = new OnUpdateNode({
  id: 'update_1',
  type: 'core/events/onUpdate'
});

// 连接到监控逻辑
updateNode.connectTo(monitorNode, 'flow');
```

**注意事项**:
- 避免在更新循环中执行重计算
- 使用条件判断减少不必要的执行
- 注意性能影响

### 2. 流程控制节点

#### BranchNode (分支节点)
```typescript
// 使用场景：条件判断
const branchNode = new BranchNode({
  id: 'branch_1',
  type: 'core/flow/branch'
});

// 设置条件
branchNode.setInputValue('condition', true);

// 连接不同路径
branchNode.connectTo(truePathNode, 'true');
branchNode.connectTo(falsePathNode, 'false');
```

**应用场景**:
- 用户输入验证
- 状态检查
- 权限控制

#### ForLoopNode (For循环节点)
```typescript
// 使用场景：批量处理
const forLoopNode = new ForLoopNode({
  id: 'loop_1',
  type: 'core/flow/forLoop'
});

// 设置循环参数
forLoopNode.setInputValue('start', 0);
forLoopNode.setInputValue('end', 10);
forLoopNode.setInputValue('step', 1);

// 连接循环体
forLoopNode.connectTo(processNode, 'loopBody');
```

**性能优化**:
- 设置合理的循环次数上限
- 避免嵌套过深的循环
- 使用break条件提前退出

### 3. 数据操作节点

#### SetVariableNode (设置变量节点)
```typescript
// 使用场景：数据存储
const setVarNode = new SetVariableNode({
  id: 'setVar_1',
  type: 'core/variable/set'
});

// 设置变量
setVarNode.setInputValue('name', 'playerScore');
setVarNode.setInputValue('value', 100);
setVarNode.setInputValue('scope', 'global');
```

**作用域说明**:
- `global`: 全局作用域，所有脚本可访问
- `local`: 局部作用域，当前脚本内有效
- `session`: 会话作用域，用户会话期间有效

#### ArrayOperationNode (数组操作节点)
```typescript
// 使用场景：数组处理
const arrayOpNode = new ArrayOperationNode({
  id: 'arrayOp_1',
  type: 'core/array/operation'
});

// 数组添加元素
arrayOpNode.setInputValue('array', [1, 2, 3]);
arrayOpNode.setInputValue('operation', 'push');
arrayOpNode.setInputValue('value', 4);
// 结果: [1, 2, 3, 4]
```

**支持的操作**:
- `push`: 添加元素到末尾
- `pop`: 移除末尾元素
- `shift`: 移除首个元素
- `unshift`: 添加元素到开头
- `slice`: 提取子数组
- `indexOf`: 查找元素索引

## 数学节点使用详解

### 基础运算节点

#### AddNode (加法节点)
```typescript
// 数值加法
const addNode = new AddNode();
addNode.setInputValue('a', 10);
addNode.setInputValue('b', 20);
const result = addNode.execute(); // 结果: 30

// 向量加法
addNode.setInputValue('a', new Vector3(1, 2, 3));
addNode.setInputValue('b', new Vector3(4, 5, 6));
// 结果: Vector3(5, 7, 9)
```

### 高级数学节点

#### TrigonometricNode (三角函数节点)
```typescript
// 计算正弦值
const sinNode = new TrigonometricNode({
  functionType: TrigonometricType.SIN
});
sinNode.setInputValue('angle', Math.PI / 2);
const result = sinNode.execute(); // 结果: 1

// 计算两点间角度
const atan2Node = new TrigonometricNode({
  functionType: TrigonometricType.ATAN2
});
atan2Node.setInputValue('y', 1);
atan2Node.setInputValue('x', 1);
// 结果: π/4 (45度)
```

#### VectorMathNode (向量数学节点)
```typescript
// 向量长度计算
const vectorNode = new VectorMathNode({
  operation: VectorOperation.LENGTH
});
vectorNode.setInputValue('vector', new Vector3(3, 4, 0));
// 结果: 5

// 向量归一化
vectorNode.setOperation(VectorOperation.NORMALIZE);
vectorNode.setInputValue('vector', new Vector3(10, 0, 0));
// 结果: Vector3(1, 0, 0)
```

## AI节点使用详解

### 语音处理节点

#### SpeechRecognitionNode (语音识别节点)
```typescript
// 语音转文本
const speechNode = new SpeechRecognitionNode({
  id: 'speech_1',
  type: 'ai/nlp/speechRecognition'
});

// 设置音频输入
speechNode.setInputValue('audioData', audioBuffer);
speechNode.setInputValue('language', 'zh-CN');

// 异步执行
const result = await speechNode.executeAsync();
console.log('识别结果:', result.text);
```

#### SpeechSynthesisNode (语音合成节点)
```typescript
// 文本转语音
const ttsNode = new SpeechSynthesisNode({
  id: 'tts_1',
  type: 'ai/nlp/speechSynthesis'
});

// 设置合成参数
ttsNode.setInputValue('text', '你好，欢迎使用DL引擎');
ttsNode.setInputValue('voice', 'zh-CN-XiaoxiaoNeural');
ttsNode.setInputValue('rate', 1.0);
ttsNode.setInputValue('pitch', 1.0);

// 生成语音
const audioResult = await ttsNode.executeAsync();
```

### 对话管理节点

#### DialogueManagementNode (对话管理节点)
```typescript
// 多轮对话管理
const dialogueNode = new DialogueManagementNode({
  id: 'dialogue_1',
  type: 'ai/nlp/dialogueManagement'
});

// 设置对话上下文
dialogueNode.setInputValue('userInput', '今天天气怎么样？');
dialogueNode.setInputValue('context', previousContext);
dialogueNode.setInputValue('knowledgeBase', weatherKB);

// 生成回复
const response = await dialogueNode.executeAsync();
console.log('AI回复:', response.text);
```

## 网络节点使用详解

### WebRTC节点

#### CreateWebRTCConnectionNode (创建WebRTC连接节点)
```typescript
// 建立P2P连接
const webrtcNode = new CreateWebRTCConnectionNode({
  id: 'webrtc_1',
  type: 'network/webrtc/createConnection'
});

// 设置连接配置
webrtcNode.setInputValue('configuration', {
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' }
  ]
});

// 建立连接
const connection = await webrtcNode.executeAsync();
```

#### GetUserMediaNode (获取用户媒体节点)
```typescript
// 获取摄像头和麦克风
const mediaNode = new GetUserMediaNode({
  id: 'media_1',
  type: 'network/webrtc/getUserMedia'
});

// 设置媒体约束
mediaNode.setInputValue('constraints', {
  video: { width: 1280, height: 720 },
  audio: true
});

// 获取媒体流
const stream = await mediaNode.executeAsync();
```

### HTTP节点

#### HTTPGetNode (HTTP GET请求节点)
```typescript
// 发送GET请求
const httpNode = new HTTPGetNode({
  id: 'http_1',
  type: 'network/http/get'
});

// 设置请求参数
httpNode.setInputValue('url', 'https://api.example.com/data');
httpNode.setInputValue('headers', {
  'Authorization': 'Bearer token123',
  'Content-Type': 'application/json'
});

// 发送请求
const response = await httpNode.executeAsync();
console.log('响应数据:', response.data);
```

## UI节点使用详解

### 基础UI组件

#### CreateButtonNode (创建按钮节点)
```typescript
// 创建交互按钮
const buttonNode = new CreateButtonNode({
  id: 'button_1',
  type: 'ui/button/create'
});

// 设置按钮属性
buttonNode.setInputValue('text', '点击我');
buttonNode.setInputValue('position', { x: 100, y: 50 });
buttonNode.setInputValue('size', { width: 120, height: 40 });
buttonNode.setInputValue('style', {
  backgroundColor: '#007bff',
  color: 'white',
  borderRadius: '4px'
});

// 设置点击事件
buttonNode.setInputValue('onClick', () => {
  console.log('按钮被点击了！');
});

// 创建按钮
const button = buttonNode.execute();
```

#### CreateDataGridNode (创建数据表格节点)
```typescript
// 创建数据表格
const gridNode = new CreateDataGridNode({
  id: 'grid_1',
  type: 'ui/advanced/dataGrid'
});

// 设置表格数据
gridNode.setInputValue('columns', [
  { field: 'id', headerName: 'ID', width: 70 },
  { field: 'name', headerName: '姓名', width: 130 },
  { field: 'age', headerName: '年龄', width: 90 }
]);

gridNode.setInputValue('rows', [
  { id: 1, name: '张三', age: 25 },
  { id: 2, name: '李四', age: 30 }
]);

// 创建表格
const grid = gridNode.execute();
```

## 物理节点使用详解

### 刚体物理

#### CreatePhysicsBodyNode (创建物理体节点)
```typescript
// 创建物理体
const physicsNode = new CreatePhysicsBodyNode({
  id: 'physics_1',
  type: 'physics/body/create'
});

// 设置物理属性
physicsNode.setInputValue('entity', targetEntity);
physicsNode.setInputValue('mass', 1.0);
physicsNode.setInputValue('friction', 0.5);
physicsNode.setInputValue('restitution', 0.3);
physicsNode.setInputValue('bodyType', 'dynamic');

// 创建物理体
const physicsBody = physicsNode.execute();
```

#### ApplyForceNode (施加力节点)
```typescript
// 对物体施加力
const forceNode = new ApplyForceNode({
  id: 'force_1',
  type: 'physics/force/apply'
});

// 设置力的参数
forceNode.setInputValue('entity', targetEntity);
forceNode.setInputValue('force', new Vector3(0, 100, 0)); // 向上的力
forceNode.setInputValue('position', new Vector3(0, 0, 0)); // 施力点
forceNode.setInputValue('mode', 'force'); // 力的类型

// 施加力
forceNode.execute();
```

## 常见使用模式

### 1. 事件驱动模式
```typescript
// 用户点击 -> 播放动画 -> 显示UI
clickEvent -> playAnimation -> showUI
```

### 2. 状态机模式
```typescript
// 状态检查 -> 状态转换 -> 执行动作
checkState -> switchState -> executeAction
```

### 3. 数据流模式
```typescript
// 数据输入 -> 处理转换 -> 输出显示
dataInput -> processData -> displayOutput
```

### 4. 异步处理模式
```typescript
// 异步请求 -> 等待响应 -> 处理结果
asyncRequest -> awaitResponse -> handleResult
```

## 性能优化建议

### 1. 节点复用
- 避免重复创建相同功能的节点
- 使用变量节点传递数据
- 合理使用子图和函数节点

### 2. 执行优化
- 减少更新循环中的计算
- 使用缓存节点存储结果
- 合理设置循环次数限制

### 3. 内存管理
- 及时释放不需要的资源
- 避免循环引用
- 使用对象池管理频繁创建的对象

### 4. 调试技巧
- 使用日志节点输出中间结果
- 设置断点进行逐步调试
- 利用性能分析节点监控执行效率

## 错误处理最佳实践

### 1. 使用TryCatch节点
```typescript
// 包装可能出错的操作
tryNode.connectTo(riskyOperation, 'try');
tryNode.connectTo(errorHandler, 'catch');
```

### 2. 输入验证
```typescript
// 验证输入数据的有效性
validateInput -> processData -> handleResult
```

### 3. 默认值设置
```typescript
// 为关键参数设置默认值
inputNode.setDefaultValue('parameter', defaultValue);
```

## 总结

DL引擎的视觉脚本系统提供了丰富的节点库，涵盖了从基础编程到高级AI功能的完整体系。通过合理使用这些节点，开发者可以快速构建复杂的交互式应用。关键是要理解每个节点的特点和适用场景，遵循最佳实践，注重性能优化和错误处理。
